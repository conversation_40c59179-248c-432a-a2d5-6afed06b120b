'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import QRScanner from '@/components/QRScanner';
import { ArrowLeft, AlertCircle, CheckCircle } from 'lucide-react';
import { useUITranslation } from '@/utils/ui-translations';

export default function ScanPage() {
  const [scanResult, setScanResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const router = useRouter();
  const { t } = useUITranslation();

  // Disable right-click and text selection
  useEffect(() => {
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      return false;
    };

    const handleSelectStart = (e: Event) => {
      e.preventDefault();
      return false;
    };

    const handleDragStart = (e: DragEvent) => {
      e.preventDefault();
      return false;
    };

    // Add event listeners
    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('selectstart', handleSelectStart);
    document.addEventListener('dragstart', handleDragStart);

    // Cleanup event listeners
    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('selectstart', handleSelectStart);
      document.removeEventListener('dragstart', handleDragStart);
    };
  }, []);

  const handleScanSuccess = async (result: string) => {
    console.log('QR Code scanned:', result);
    setScanResult(result);
    setError(null);
    setIsProcessing(true);

    try {
      // Check if this is an AstroConnect QR code
      const url = new URL(result);
      
      // Check if it's our domain and has the correct format
      if (url.pathname.startsWith('/qr/')) {
        const token = url.pathname.split('/qr/')[1];
        if (token) {
          // Redirect to the QR token page
          router.push(`/qr/${token}`);
          return;
        }
      }
      
      // If it's not an AstroConnect QR code, show error
      setError(t('invalid_qr_code'));
    } catch (err) {
      // If URL parsing fails, it's not a valid URL
      setError(t('invalid_qr_code'));
    } finally {
      setIsProcessing(false);
    }
  };

  const handleScanError = (error: string) => {
    console.error('QR Scan error:', error);
    setError(error);
    setScanResult(null);
    setIsProcessing(false);
  };

  const handleBack = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 relative overflow-hidden">
      {/* Animated background stars */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-white rounded-full animate-pulse opacity-70"></div>
        <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-yellow-300 rounded-full animate-ping opacity-60"></div>
        <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-purple-300 rounded-full animate-pulse opacity-50"></div>
        <div className="absolute top-1/2 right-1/4 w-1 h-1 bg-blue-300 rounded-full animate-ping opacity-40"></div>
        <div className="absolute bottom-1/3 right-1/2 w-2 h-2 bg-indigo-300 rounded-full animate-pulse opacity-60"></div>
      </div>

      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 md:p-6">
          <button
            onClick={handleBack}
            className="flex items-center space-x-2 text-white hover:text-purple-300 transition-colors"
          >
            <ArrowLeft size={20} />
            <span className="text-sm font-medium">{t('back')}</span>
          </button>
          
          <h1 className="text-xl md:text-2xl font-bold text-white text-center flex-1">
            {t('scan_qr_code')}
          </h1>
          
          <div className="w-16"></div> {/* Spacer for centering */}
        </div>

        {/* Main content */}
        <div className="flex-1 flex flex-col items-center justify-center p-4 space-y-6">
          {/* Status messages */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4 max-w-md w-full">
              <div className="flex items-center space-x-3">
                <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
                <div>
                  <p className="text-red-300 font-medium">{t('scan_error')}</p>
                  <p className="text-red-200 text-sm mt-1">{error}</p>
                </div>
              </div>
            </div>
          )}

          {scanResult && !error && (
            <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4 max-w-md w-full">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                <div>
                  <p className="text-green-300 font-medium">{t('scan_success')}</p>
                  <p className="text-green-200 text-sm mt-1">{t('redirecting')}</p>
                </div>
              </div>
            </div>
          )}

          {isProcessing && (
            <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4 max-w-md w-full">
              <div className="flex items-center space-x-3">
                <div className="w-5 h-5 border-2 border-blue-400 border-t-transparent rounded-full animate-spin flex-shrink-0"></div>
                <p className="text-blue-300">{t('processing')}</p>
              </div>
            </div>
          )}

          {/* QR Scanner */}
          <div className="w-full max-w-md">
            <QRScanner
              onScanSuccess={handleScanSuccess}
              onScanError={handleScanError}
              width={300}
              height={300}
            />
          </div>

          {/* Instructions */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 max-w-md w-full border border-white/20">
            <h3 className="text-white font-semibold mb-2">{t('scan_instructions')}</h3>
            <ul className="text-gray-300 text-sm space-y-1">
              <li>• {t('position_qr_code')}</li>
              <li>• {t('ensure_good_lighting')}</li>
              <li>• {t('hold_steady')}</li>
            </ul>
          </div>

          {/* Help note */}
          <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-3 max-w-md w-full">
            <p className="text-blue-300 text-xs text-center">
              💡 {t('qr_code_hint')}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
