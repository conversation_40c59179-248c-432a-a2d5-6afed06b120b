'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

import PWAInstaller from '@/components/PWAInstaller';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { useLanguage } from '@/hooks/useLanguage';
import { <PERSON>rk<PERSON>, Moon, Star, Zap } from 'lucide-react';
import { useUITranslation } from '@/utils/ui-translations';

export default function Home() {

  const { setLanguage } = useLanguage();
  const router = useRouter();
  const { t } = useUITranslation();

  const handleLanguageChange = (newLanguage: 'en' | 'si') => {
    setLanguage(newLanguage);
  };

  // Disable right-click and text selection
  useEffect(() => {
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      return false;
    };

    const handleSelectStart = (e: Event) => {
      e.preventDefault();
      return false;
    };

    const handleDragStart = (e: DragEvent) => {
      e.preventDefault();
      return false;
    };

    // Add event listeners
    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('selectstart', handleSelectStart);
    document.addEventListener('dragstart', handleDragStart);

    // Cleanup event listeners
    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('selectstart', handleSelectStart);
      document.removeEventListener('dragstart', handleDragStart);
    };
  }, []);



  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 relative overflow-hidden no-select no-context-menu">
      {/* Language Switcher - Top Right */}
      <div className="absolute top-4 right-4 md:top-6 md:right-6 z-50">
        <LanguageSwitcher onLanguageChange={handleLanguageChange} />
      </div>

      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-white rounded-full animate-pulse opacity-70"></div>
        <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-yellow-300 rounded-full animate-ping opacity-60"></div>
        <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-purple-300 rounded-full animate-pulse opacity-50"></div>
        <div className="absolute top-1/2 right-1/4 w-1 h-1 bg-blue-300 rounded-full animate-ping opacity-40"></div>
        <div className="absolute bottom-1/3 right-1/2 w-2 h-2 bg-indigo-300 rounded-full animate-pulse opacity-60"></div>
      </div>

      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen p-4 pt-20 md:pt-4">
        <div className="text-center max-w-4xl mx-auto">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center justify-center mb-4">
                <Sparkles className="w-8 h-8 text-yellow-400 mr-2" />
                <h1 className="text-4xl md:text-6xl font-bold text-white">
                  AstroConnect
                </h1>
                <Sparkles className="w-8 h-8 text-yellow-400 ml-2" />
              </div>
              <p className="text-xl md:text-2xl text-gray-300 mb-2">
                {t('your_personal_horoscope_daily_guide')}
              </p>
              <p className="text-gray-400">
                {t('discover_cosmic_destiny')}
              </p>
            </div>

            {/* Features */}
            <div className="grid md:grid-cols-3 gap-6 mb-12">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <Moon className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">{t('daily_horoscopes')}</h3>
                <p className="text-gray-300 text-sm">
                  {t('daily_horoscopes_description')}
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <Star className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">{t('lucky_guidance')}</h3>
                <p className="text-gray-300 text-sm">
                  {t('lucky_guidance_description')}
                </p>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
                <Zap className="w-12 h-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">{t('qr_access')}</h3>
                <p className="text-gray-300 text-sm">
                  {t('qr_access_description')}
                </p>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="space-y-4">
              <button
                onClick={() => router.push('/scan')}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
              >
                {t('scan_qr_code')}
              </button>

              <div className="text-center space-y-2">
                <p className="text-gray-400 text-sm">
                  {t('qr_card_description')}
                </p>
                <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-3 max-w-md mx-auto">
                  <p className="text-blue-300 text-xs">
                    {t('qr_compatibility_tip')}
                  </p>
                </div>
              </div>
            </div>

        </div>
      </div>

      {/* PWA Installer */}
      <PWAInstaller />
    </div>
  );
}
